#!/bin/bash
# HiberProxy Enhanced Environment Activation Script

VENV_NAME="hiber_venv"

if [ -d "$VENV_NAME" ]; then
    echo "Activating HiberProxy Enhanced virtual environment..."
    source "$VENV_NAME/bin/activate"
    echo "Virtual environment activated!"
    echo "Python path: $(which python)"
    echo "Pip path: $(which pip)"
    echo ""
    echo "To start the web interface:"
    echo "  python hiber_proxy/web/run_server.py"
    echo ""
    echo "To deactivate the environment:"
    echo "  deactivate"
else
    echo "Error: Virtual environment not found!"
    echo "Please run setup_web_env.py first"
    exit 1
fi
