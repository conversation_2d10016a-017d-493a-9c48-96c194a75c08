source activate_hiber_env.sh && python hiber_proxy/web/run_server.py
Activating HiberProxy Enhanced virtual environment...
Virtual environment activated!
Python path: /home/<USER>/Documents/Hibernet/hiber_venv/bin/python
Pip path: /home/<USER>/Documents/Hibernet/hiber_venv/bin/pip

To start the web interface:
  python hiber_proxy/web/run_server.py

To deactivate the environment:
  deactivate
🚀 Starting HiberProxy Enhanced Web Interface...

✓ Running in virtual environment: /home/<USER>/Documents/Hibernet/hiber_venv
✓ All dependencies available

[16:33:11] INFO     hiber_proxy.main - HiberProxy Enhanced logging system initialized
[16:33:11] INFO     hiber_proxy.main - Log level: INFO
[16:33:11] INFO     hiber_proxy.main - Log directory: /home/<USER>/Documents/Hibernet/logs
[16:33:11] INFO     hiber_proxy.main - Console logging: True
[16:33:11] INFO     hiber_proxy.main - File logging: True
[16:33:11] INFO     hiber_proxy.main - JSON logging: False
[16:33:11] INFO     hiber_proxy.main - HiberProxy Enhanced logging system initialized
[16:33:11] INFO     hiber_proxy.main - Log level: INFO
[16:33:11] INFO     hiber_proxy.main - Log directory: logs
[16:33:11] INFO     hiber_proxy.main - Console logging: True
[16:33:11] INFO     hiber_proxy.main - File logging: True
[16:33:11] INFO     hiber_proxy.main - JSON logging: False
[16:33:11] INFO     hiber_proxy.main - HiberProxy Enhanced starting up...
[16:33:11] INFO     hiber_proxy.core.database - Database manager initialized: hiber_proxy.db (timeout: 30s)
[16:33:11] INFO     hiber_proxy.core.user_agents - Initialized user agent database with 22 agents
[16:33:11] INFO     hiber_proxy.core.memory_manager - Memory monitoring started
[16:33:11] INFO     hiber_proxy.core.proxy_lists - Proxy list manager initialized with storage: proxy_lists
[16:33:11] INFO     hiber_proxy.scrapers.github_scrapers - GitHub proxy scrapers initialized
[16:33:11] INFO     hiber_proxy.main - HiberProxy Enhanced initialized successfully
Web interface will be available at: http://0.0.0.0:5000
Press Ctrl+C to stop the server
 * Serving Flask app 'hiber_proxy.web.app'
 * Debug mode: off
[16:33:11] INFO     werkzeug - WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://*********:5000
[16:33:11] INFO     werkzeug - Press CTRL+C to quit
[16:33:18] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:18] "GET / HTTP/1.1" 200 -
[16:33:18] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:18] "GET /static/css/style.css HTTP/1.1" 200 -
[16:33:18] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:18] "GET /static/js/main.js HTTP/1.1" 200 -
[16:33:18] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:18] "GET /static/js/websocket.js HTTP/1.1" 200 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qbXo HTTP/1.1" 200 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "GET /api/stats HTTP/1.1" 200 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "GET /api/stats HTTP/1.1" 200 -
[16:33:19] INFO     hiber_proxy.web - Client connected to WebSocket
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qbYM&sid=3ebck5YfmhGH9yMrAAAA HTTP/1.1" 200 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "GET /api/protocol-stats HTTP/1.1" 200 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qbYO&sid=3ebck5YfmhGH9yMrAAAA HTTP/1.1" 200 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "GET /favicon.ico HTTP/1.1" 404 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qbZ5&sid=3ebck5YfmhGH9yMrAAAA HTTP/1.1" 200 -
[16:33:19] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:19] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qbZ6&sid=3ebck5YfmhGH9yMrAAAA HTTP/1.1" 200 -
[16:33:25] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /socket.io/?EIO=4&transport=websocket&sid=3ebck5YfmhGH9yMrAAAA HTTP/1.1" 200 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /proxies HTTP/1.1" 200 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /static/css/style.css HTTP/1.1" 304 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /static/js/main.js HTTP/1.1" 304 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qd3o HTTP/1.1" 200 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /api/stats HTTP/1.1" 200 -
[16:33:25] INFO     hiber_proxy.web - Client connected to WebSocket
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qd4P&sid=y6vc_r0jngEP2LevAAAC HTTP/1.1" 200 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qd4Q&sid=y6vc_r0jngEP2LevAAAC HTTP/1.1" 200 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /api/proxies?limit=100 HTTP/1.1" 200 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qd4r&sid=y6vc_r0jngEP2LevAAAC HTTP/1.1" 200 -
[16:33:25] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:25] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qd4s&sid=y6vc_r0jngEP2LevAAAC HTTP/1.1" 200 -
[16:33:31] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /socket.io/?EIO=4&transport=websocket&sid=y6vc_r0jngEP2LevAAAC HTTP/1.1" 200 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /config HTTP/1.1" 200 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /static/css/style.css HTTP/1.1" 304 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /static/js/main.js HTTP/1.1" 304 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qebD HTTP/1.1" 200 -
[16:33:31] INFO     hiber_proxy.web - Client connected to WebSocket
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qebO&sid=12-qa7Yx1MMYMEv6AAAE HTTP/1.1" 200 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qebP&sid=12-qa7Yx1MMYMEv6AAAE HTTP/1.1" 200 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /api/stats HTTP/1.1" 200 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qebm&sid=12-qa7Yx1MMYMEv6AAAE HTTP/1.1" 200 -
[16:33:31] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:31] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qebn&sid=12-qa7Yx1MMYMEv6AAAE HTTP/1.1" 200 -
[16:33:33] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:33:33] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:33] "GET /socket.io/?EIO=4&transport=websocket&sid=12-qa7Yx1MMYMEv6AAAE HTTP/1.1" 200 -
[16:33:33] ERROR    hiber_proxy.web.app - Exception on /analytics [GET]
Traceback (most recent call last):
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask_cors/extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_proxy/web/app.py", line 72, in analytics
    return render_template('analytics.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/loaders.py", line 138, in load
    code = environment.compile(source, name, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 771, in compile
    self.handle_exception(source=source_hint)
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/Documents/Hibernet/hiber_proxy/web/templates/analytics.html", line 267, in template
    {% block scripts %}
jinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
[16:33:33] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:33] "GET /analytics HTTP/1.1" 500 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /proxies HTTP/1.1" 200 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /static/js/main.js HTTP/1.1" 304 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /static/css/style.css HTTP/1.1" 304 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qg6I HTTP/1.1" 200 -
[16:33:37] INFO     hiber_proxy.web - Client connected to WebSocket
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qg6a&sid=JqhJWT69cEl0ygMMAAAG HTTP/1.1" 200 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qg6b&sid=JqhJWT69cEl0ygMMAAAG HTTP/1.1" 200 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /api/stats HTTP/1.1" 200 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qg6s&sid=JqhJWT69cEl0ygMMAAAG HTTP/1.1" 200 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qg6s.0&sid=JqhJWT69cEl0ygMMAAAG HTTP/1.1" 200 -
[16:33:37] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:37] "GET /api/proxies?limit=100 HTTP/1.1" 200 -
[16:33:39] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /socket.io/?EIO=4&transport=websocket&sid=JqhJWT69cEl0ygMMAAAG HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET / HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /static/js/main.js HTTP/1.1" 304 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /static/css/style.css HTTP/1.1" 304 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qgTO HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /api/stats HTTP/1.1" 200 -
[16:33:39] INFO     hiber_proxy.web - Client connected to WebSocket
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qgTh&sid=60yOYZPkC8BYeOjcAAAI HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /api/protocol-stats HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qgTi&sid=60yOYZPkC8BYeOjcAAAI HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /api/stats HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "GET /socket.io/?EIO=4&transport=polling&t=PZ3qgU8.0&sid=60yOYZPkC8BYeOjcAAAI HTTP/1.1" 200 -
[16:33:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:39] "POST /socket.io/?EIO=4&transport=polling&t=PZ3qgU8&sid=60yOYZPkC8BYeOjcAAAI HTTP/1.1" 200 -
[16:33:44] ERROR    hiber_proxy.web - Error checking proxies: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
[16:33:44] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:44] "POST /api/check HTTP/1.1" 500 -
[16:33:44] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:33:44] "GET /api/stats HTTP/1.1" 200 -
[16:34:09] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:09] "GET /api/stats HTTP/1.1" 200 -
[16:34:09] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:09] "GET /api/stats HTTP/1.1" 200 -
[16:34:20] ERROR    hiber_proxy.web - Error downloading proxies: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
[16:34:20] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:20] "POST /api/download HTTP/1.1" 500 -
[16:34:20] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:20] "GET /api/stats HTTP/1.1" 200 -
[16:34:20] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:20] "GET /api/protocol-stats HTTP/1.1" 200 -
[16:34:34] ERROR    hiber_proxy.web - Error downloading proxies: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
[16:34:34] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:34] "POST /api/download HTTP/1.1" 500 -
[16:34:34] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:34] "GET /api/stats HTTP/1.1" 200 -
[16:34:34] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:34] "GET /api/protocol-stats HTTP/1.1" 200 -
[16:34:36] ERROR    hiber_proxy.web - Error checking proxies: 415 Unsupported Media Type: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
[16:34:36] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:36] "POST /api/check HTTP/1.1" 500 -
[16:34:36] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:36] "GET /api/stats HTTP/1.1" 200 -
[16:34:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:39] "GET /api/stats HTTP/1.1" 200 -
[16:34:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:39] "GET /api/stats HTTP/1.1" 200 -
[16:34:59] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:34:59] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:34:59] "GET /socket.io/?EIO=4&transport=websocket&sid=60yOYZPkC8BYeOjcAAAI HTTP/1.1" 200 -
[16:35:00] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:00] "GET /api/export HTTP/1.1" 200 -
[16:35:09] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:09] "GET /api/stats HTTP/1.1" 200 -
[16:35:09] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:09] "GET /api/stats HTTP/1.1" 200 -
[16:35:18] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:18] "GET / HTTP/1.1" 200 -
[16:35:18] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:18] "GET /static/js/main.js HTTP/1.1" 200 -
[16:35:18] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:18] "GET /static/js/websocket.js HTTP/1.1" 200 -
[16:35:18] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:18] "GET /static/css/style.css HTTP/1.1" 200 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r2q8 HTTP/1.1" 200 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "GET /api/stats HTTP/1.1" 200 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "GET /api/stats HTTP/1.1" 200 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "GET /api/protocol-stats HTTP/1.1" 200 -
[16:35:19] INFO     hiber_proxy.web - Client connected to WebSocket
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r2qm&sid=lLFX47493e3boFV6AAAK HTTP/1.1" 200 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r2qn&sid=lLFX47493e3boFV6AAAK HTTP/1.1" 200 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "GET /favicon.ico HTTP/1.1" 404 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r2rj&sid=lLFX47493e3boFV6AAAK HTTP/1.1" 200 -
[16:35:19] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:19] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r2rk&sid=lLFX47493e3boFV6AAAK HTTP/1.1" 200 -
[16:35:27] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /socket.io/?EIO=4&transport=websocket&sid=lLFX47493e3boFV6AAAK HTTP/1.1" 200 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /proxies HTTP/1.1" 200 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /static/js/main.js HTTP/1.1" 304 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /static/css/style.css HTTP/1.1" 304 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r4x2 HTTP/1.1" 200 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /api/stats HTTP/1.1" 200 -
[16:35:27] INFO     hiber_proxy.web - Client connected to WebSocket
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r4xZ&sid=cx_cQ_cj104BU_3EAAAM HTTP/1.1" 200 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r4xa&sid=cx_cQ_cj104BU_3EAAAM HTTP/1.1" 200 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r4xx&sid=cx_cQ_cj104BU_3EAAAM HTTP/1.1" 200 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r4xy&sid=cx_cQ_cj104BU_3EAAAM HTTP/1.1" 200 -
[16:35:27] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:27] "GET /api/proxies?limit=100 HTTP/1.1" 200 -
[16:35:28] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /socket.io/?EIO=4&transport=websocket&sid=cx_cQ_cj104BU_3EAAAM HTTP/1.1" 200 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /config HTTP/1.1" 200 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /static/css/style.css HTTP/1.1" 304 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /static/js/main.js HTTP/1.1" 304 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r59u HTTP/1.1" 200 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /api/stats HTTP/1.1" 200 -
[16:35:28] INFO     hiber_proxy.web - Client connected to WebSocket
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r5A6&sid=h3N48sXEhH1VQHinAAAO HTTP/1.1" 200 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r5A6.0&sid=h3N48sXEhH1VQHinAAAO HTTP/1.1" 200 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r5AV&sid=h3N48sXEhH1VQHinAAAO HTTP/1.1" 200 -
[16:35:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:28] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r5AV.0&sid=h3N48sXEhH1VQHinAAAO HTTP/1.1" 200 -
[16:35:37] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /socket.io/?EIO=4&transport=websocket&sid=h3N48sXEhH1VQHinAAAO HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET / HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /static/js/main.js HTTP/1.1" 304 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /static/css/style.css HTTP/1.1" 304 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r7El HTTP/1.1" 200 -
[16:35:37] INFO     hiber_proxy.web - Client connected to WebSocket
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r7F2&sid=QWTV50tjTc7AeOfMAAAQ HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /api/stats HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r7F2.0&sid=QWTV50tjTc7AeOfMAAAQ HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /api/protocol-stats HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r7Fd&sid=QWTV50tjTc7AeOfMAAAQ HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r7Fc&sid=QWTV50tjTc7AeOfMAAAQ HTTP/1.1" 200 -
[16:35:37] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:37] "GET /api/stats HTTP/1.1" 200 -
[16:35:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:39] "GET /api/stats HTTP/1.1" 200 -
[16:35:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:39] "GET /api/stats HTTP/1.1" 200 -
[16:35:41] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /socket.io/?EIO=4&transport=websocket&sid=QWTV50tjTc7AeOfMAAAQ HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /proxies HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /static/css/style.css HTTP/1.1" 304 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /static/js/main.js HTTP/1.1" 304 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8GU HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /api/stats HTTP/1.1" 200 -
[16:35:41] INFO     hiber_proxy.web - Client connected to WebSocket
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r8Gx&sid=KvRPt8DTbJJ30otUAAAS HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8Gx.0&sid=KvRPt8DTbJJ30otUAAAS HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8HL.0&sid=KvRPt8DTbJJ30otUAAAS HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r8HL&sid=KvRPt8DTbJJ30otUAAAS HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /api/proxies?limit=100 HTTP/1.1" 200 -
[16:35:41] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /socket.io/?EIO=4&transport=websocket&sid=KvRPt8DTbJJ30otUAAAS HTTP/1.1" 200 -
[16:35:41] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:41] "GET /config HTTP/1.1" 200 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /static/js/websocket.js HTTP/1.1" 304 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /static/css/style.css HTTP/1.1" 304 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /static/js/main.js HTTP/1.1" 304 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8RO HTTP/1.1" 200 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /api/stats HTTP/1.1" 200 -
[16:35:42] INFO     hiber_proxy.web - Client connected to WebSocket
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r8Rr&sid=4yLYXHqdfvfvkFzAAAAU HTTP/1.1" 200 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8Rs&sid=4yLYXHqdfvfvkFzAAAAU HTTP/1.1" 200 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8SB&sid=4yLYXHqdfvfvkFzAAAAU HTTP/1.1" 200 -
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r8SA&sid=4yLYXHqdfvfvkFzAAAAU HTTP/1.1" 200 -
[16:35:42] INFO     hiber_proxy.web - Client disconnected from WebSocket
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /socket.io/?EIO=4&transport=websocket&sid=4yLYXHqdfvfvkFzAAAAU HTTP/1.1" 200 -
[16:35:42] ERROR    hiber_proxy.web.app - Exception on /analytics [GET]
Traceback (most recent call last):
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask_cors/extension.py", line 176, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
                                                ^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
         ^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_proxy/web/app.py", line 72, in analytics
    return render_template('analytics.html')
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/flask/templating.py", line 149, in render_template
    template = app.jinja_env.get_or_select_template(template_name_or_list)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 1087, in get_or_select_template
    return self.get_template(template_name_or_list, parent, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 1016, in get_template
    return self._load_template(name, globals)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 975, in _load_template
    template = self.loader.load(self, name, self.make_globals(globals))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/loaders.py", line 138, in load
    code = environment.compile(source, name, filename)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 771, in compile
    self.handle_exception(source=source_hint)
  File "/home/<USER>/Documents/Hibernet/hiber_venv/lib/python3.12/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/Documents/Hibernet/hiber_proxy/web/templates/analytics.html", line 267, in template
    {% block scripts %}
jinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.
[16:35:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:35:42] "GET /analytics HTTP/1.1" 500 -
[16:35:43] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:43] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8sX HTTP/1.1" 200 -
[16:35:43] INFO     hiber_proxy.web - Client connected to WebSocket
[16:35:43] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:43] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r8sj&sid=mJ2RzehcCKnbMkFDAAAW HTTP/1.1" 200 -
[16:35:43] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:43] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8sk&sid=mJ2RzehcCKnbMkFDAAAW HTTP/1.1" 200 -
[16:35:43] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:43] "POST /socket.io/?EIO=4&transport=polling&t=PZ3r8sw&sid=mJ2RzehcCKnbMkFDAAAW HTTP/1.1" 200 -
[16:35:43] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:35:43] "GET /socket.io/?EIO=4&transport=polling&t=PZ3r8sx&sid=mJ2RzehcCKnbMkFDAAAW HTTP/1.1" 200 -
[16:36:09] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:36:09] "GET /api/stats HTTP/1.1" 200 -
[16:36:09] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:36:09] "GET /api/stats HTTP/1.1" 200 -
[16:36:12] INFO     werkzeug - ********* - - [19/Aug/2025 16:36:12] "GET /api/stats HTTP/1.1" 200 -
[16:36:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:36:28] "GET /socket.io/?EIO=4&transport=polling&t=PZ3rJrf HTTP/1.1" 200 -
[16:36:28] INFO     hiber_proxy.web - Client connected to WebSocket
[16:36:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:36:28] "POST /socket.io/?EIO=4&transport=polling&t=PZ3rJrm&sid=d6LsDxeDXSe8YLShAAAY HTTP/1.1" 200 -
[16:36:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:36:28] "GET /socket.io/?EIO=4&transport=polling&t=PZ3rJrm.0&sid=d6LsDxeDXSe8YLShAAAY HTTP/1.1" 200 -
[16:36:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:36:28] "POST /socket.io/?EIO=4&transport=polling&t=PZ3rJrx&sid=d6LsDxeDXSe8YLShAAAY HTTP/1.1" 200 -
[16:36:28] INFO     werkzeug - ********* - - [19/Aug/2025 16:36:28] "GET /socket.io/?EIO=4&transport=polling&t=PZ3rJrx.0&sid=d6LsDxeDXSe8YLShAAAY HTTP/1.1" 200 -
[16:36:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:36:39] "GET /api/stats HTTP/1.1" 200 -
[16:36:39] INFO     werkzeug - 127.0.0.1 - - [19/Aug/2025 16:36:39] "GET /api/stats HTTP/1.1" 200 -
[16:36:42] INFO     werkzeug - ********* - - [19/Aug/2025 16:36:42] "GET /api/stats HTTP/1.1" 200 -
