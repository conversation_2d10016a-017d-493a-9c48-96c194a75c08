"""
Flask web application for HiberProxy Enhanced

Provides web interface with dark orange terminal aesthetic for proxy management,
configuration, and analytics with real-time updates via WebSocket.
"""

import os
import sys
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file
from flask_socketio import Socket<PERSON>, emit
from flask_cors import CORS

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from hiber_proxy.main import HiberProxyApp
from hiber_proxy.core.logging_config import setup_logging, get_logger
from .api.routes import api_bp

def create_app(config_path=None):
    """Create and configure Flask application"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'hiber-proxy-enhanced-secret-key'
    
    # Enable CORS for API endpoints
    CORS(app)
    
    # Initialize SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    # Initialize HiberProxy app
    hiber_app = HiberProxyApp(config_path)
    
    # Setup logging
    logger = get_logger('web')
    
    # Store app instances for access in routes
    app.hiber_app = hiber_app
    app.socketio = socketio
    app.logger_instance = logger
    
    # Register routes
    register_routes(app)
    app.register_blueprint(api_bp)
    register_socketio_events(socketio, hiber_app, logger)
    
    return app, socketio

def register_routes(app):
    """Register main web routes"""

    @app.route('/')
    def index():
        """Main dashboard"""
        return render_template('index.html')

    @app.route('/proxies')
    def proxies():
        """Proxy management page"""
        return render_template('proxies.html')

    @app.route('/config')
    def config():
        """Configuration page"""
        return render_template('config.html')

    @app.route('/analytics')
    def analytics():
        """Analytics dashboard"""
        return render_template('analytics.html')

def register_socketio_events(socketio, hiber_app, logger):
    """Register SocketIO events for real-time updates"""
    
    @socketio.on('connect')
    def handle_connect():
        """Handle client connection"""
        logger.info("Client connected to WebSocket")
        emit('status', {'message': 'Connected to HiberProxy Enhanced'})
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Handle client disconnection"""
        logger.info("Client disconnected from WebSocket")
    
    @socketio.on('get_stats')
    def handle_get_stats():
        """Handle real-time stats request"""
        try:
            stats = hiber_app.get_statistics()
            emit('stats_update', stats)
        except Exception as e:
            logger.error(f"Error getting stats for WebSocket: {e}")
            emit('error', {'message': str(e)})

if __name__ == '__main__':
    app, socketio = create_app()
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
