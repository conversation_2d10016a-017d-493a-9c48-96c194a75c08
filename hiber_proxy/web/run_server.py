#!/usr/bin/env python3
"""
Web server launcher for HiberProxy Enhanced

Starts the Flask web interface with proper configuration and error handling.
"""

import sys
import os
from pathlib import Path

# Add parent directories to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from hiber_proxy.web.app import create_app
from hiber_proxy.core.logging_config import setup_logging

def main():
    """Main entry point for web server"""
    print("Starting HiberProxy Enhanced Web Interface...")
    
    # Setup logging
    setup_logging(log_level="INFO", enable_console=True, enable_file=True)
    
    try:
        # Create Flask app with SocketIO
        app, socketio = create_app()
        
        # Configuration
        host = os.environ.get('HIBER_WEB_HOST', '0.0.0.0')
        port = int(os.environ.get('HIBER_WEB_PORT', 5000))
        debug = os.environ.get('HIBER_WEB_DEBUG', 'False').lower() == 'true'
        
        print(f"Web interface will be available at: http://{host}:{port}")
        print("Press Ctrl+C to stop the server")
        
        # Start the server
        socketio.run(
            app,
            host=host,
            port=port,
            debug=debug,
            allow_unsafe_werkzeug=True  # For development
        )
        
    except KeyboardInterrupt:
        print("\nShutting down web server...")
    except Exception as e:
        print(f"Error starting web server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
