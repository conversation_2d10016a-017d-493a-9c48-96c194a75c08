/* HiberProxy Enhanced - Dark Orange Terminal Theme */

/* CSS Variables for consistent theming */
:root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #3a3a3a;
    --orange-primary: #ff6600;
    --orange-secondary: #ff8533;
    --orange-light: #ffaa66;
    --orange-dark: #cc5500;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-color: #444444;
    --success-color: #00ff00;
    --error-color: #ff0000;
    --warning-color: #ffff00;
    --font-mono: 'Courier New', 'Monaco', 'Menlo', monospace;
    --font-size-base: 14px;
    --border-radius: 4px;
    --transition: all 0.2s ease;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-mono);
    font-size: var(--font-size-base);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.4;
    overflow-x: hidden;
}

/* Container and layout */
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header styles */
.header {
    background-color: var(--bg-secondary);
    border-bottom: 2px solid var(--orange-primary);
    padding: 1rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--text-primary);
}

.logo-bracket {
    color: var(--orange-primary);
}

.logo-text {
    color: var(--text-primary);
    margin: 0 0.5rem;
}

.header-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.status-indicator {
    color: var(--success-color);
    font-size: 1.2rem;
}

.status-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Navigation styles */
.navigation {
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
}

.nav-content {
    display: flex;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link:hover {
    background-color: var(--bg-secondary);
    color: var(--orange-light);
}

.nav-link.active {
    background-color: var(--orange-primary);
    color: var(--text-primary);
}

.nav-icon {
    font-size: 0.9rem;
}

/* Main content */
.main-content {
    flex: 1;
    padding: 2rem 1rem;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

/* Footer styles */
.footer {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
    margin-top: auto;
}

.footer-content {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    max-width: 1200px;
    margin: 0 auto;
    font-size: 0.9rem;
}

.footer-text {
    color: var(--text-secondary);
}

.footer-separator {
    color: var(--orange-primary);
}

/* Terminal-style components */
.terminal-window {
    background-color: var(--bg-secondary);
    border: 2px solid var(--orange-primary);
    border-radius: var(--border-radius);
    margin: 1rem 0;
}

.terminal-header {
    background-color: var(--orange-primary);
    color: var(--text-primary);
    padding: 0.5rem 1rem;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.terminal-content {
    padding: 1rem;
    font-family: var(--font-mono);
    line-height: 1.6;
}

/* Buttons */
.btn {
    background-color: var(--orange-primary);
    color: var(--text-primary);
    border: none;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    font-family: var(--font-mono);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn:hover {
    background-color: var(--orange-secondary);
}

.btn:active {
    background-color: var(--orange-dark);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-secondary);
    border-color: var(--orange-primary);
}

/* Forms */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-input {
    width: 100%;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem;
    border-radius: var(--border-radius);
    font-family: var(--font-mono);
    font-size: var(--font-size-base);
}

.form-input:focus {
    outline: none;
    border-color: var(--orange-primary);
    box-shadow: 0 0 0 2px rgba(255, 102, 0, 0.2);
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--bg-secondary);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: var(--orange-primary);
    color: var(--text-primary);
    font-weight: bold;
}

.table tr:hover {
    background-color: var(--bg-tertiary);
}

/* Status indicators */
.status-working {
    color: var(--success-color);
}

.status-failed {
    color: var(--error-color);
}

.status-unknown {
    color: var(--warning-color);
}

/* Responsive design */
@media (max-width: 768px) {
    .nav-content {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .footer-content {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .main-content {
        padding: 1rem 0.5rem;
    }
}
