# HiberProxy Enhanced Requirements
# Core dependencies for the enhanced multi-protocol proxy management system

# Web scraping and parsing
beautifulsoup4>=4.9.0
requests>=2.25.0
urllib3>=1.26.0

# Data handling and validation
pyyaml>=5.4.0

# Database (SQLite is built-in to Python)
# No additional database dependencies needed for SQLite

# Networking and protocols
# All networking libraries are built-in to Python (socket, ssl, urllib, etc.)

# Web interface dependencies
flask>=2.0.0
flask-socketio>=5.0.0
flask-cors>=3.0.0

# Optional: For advanced features (can be installed separately)
# geoip2>=4.0.0          # For IP geolocation
# maxminddb>=2.0.0       # For MaxMind GeoIP databases
# cryptography>=3.4.0    # For advanced SSL/TLS features
# aiohttp>=3.7.0         # For async HTTP operations (future enhancement)

# Development and testing (optional)
# pytest>=6.0.0
# pytest-cov>=2.10.0
# black>=21.0.0
# flake8>=3.8.0
