#!/usr/bin/env python3
"""
HiberProxy Enhanced Web Interface Setup Script (Python version)
Cross-platform setup script for creating virtual environment and installing dependencies
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

# Configuration
PROJECT_NAME = "HiberProxy Enhanced"
VENV_NAME = "hiber_venv"
PYTHON_CMD = sys.executable

# Colors for terminal output
class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_colored(message, color=Colors.NC):
    """Print colored message to terminal"""
    print(f"{color}{message}{Colors.NC}")

def run_command(command, check=True, capture_output=False):
    """Run shell command with error handling"""
    try:
        if capture_output:
            result = subprocess.run(command, shell=True, check=check, 
                                  capture_output=True, text=True)
            return result.stdout.strip()
        else:
            subprocess.run(command, shell=True, check=check)
            return True
    except subprocess.CalledProcessError as e:
        print_colored(f"Error running command: {command}", Colors.RED)
        print_colored(f"Error: {e}", Colors.RED)
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print_colored("Error: Python 3.7+ is required", Colors.RED)
        print_colored(f"Current version: {version.major}.{version.minor}.{version.micro}", Colors.RED)
        return False
    
    print_colored(f"Found Python version: {version.major}.{version.minor}.{version.micro}", Colors.BLUE)
    return True

def create_virtual_environment():
    """Create Python virtual environment"""
    print_colored(f"Creating virtual environment: {VENV_NAME}", Colors.BLUE)
    
    # Remove existing virtual environment
    venv_path = Path(VENV_NAME)
    if venv_path.exists():
        print_colored("Virtual environment already exists. Removing old one...", Colors.YELLOW)
        import shutil
        shutil.rmtree(venv_path)
    
    # Create new virtual environment
    if not run_command(f'"{PYTHON_CMD}" -m venv {VENV_NAME}'):
        print_colored("Failed to create virtual environment", Colors.RED)
        print_colored("Try installing python3-venv: sudo apt install python3-venv", Colors.YELLOW)
        return False
    
    print_colored("✓ Virtual environment created", Colors.GREEN)
    return True

def get_venv_python():
    """Get path to Python executable in virtual environment"""
    if platform.system() == "Windows":
        return os.path.join(VENV_NAME, "Scripts", "python.exe")
    else:
        return os.path.join(VENV_NAME, "bin", "python")

def get_venv_pip():
    """Get path to pip executable in virtual environment"""
    if platform.system() == "Windows":
        return os.path.join(VENV_NAME, "Scripts", "pip.exe")
    else:
        return os.path.join(VENV_NAME, "bin", "pip")

def install_dependencies():
    """Install all required dependencies"""
    venv_pip = get_venv_pip()
    
    print_colored("Upgrading pip...", Colors.BLUE)
    if not run_command(f'"{venv_pip}" install --upgrade pip'):
        return False
    
    print_colored("Installing core dependencies...", Colors.BLUE)
    if not run_command(f'"{venv_pip}" install wheel setuptools'):
        return False
    
    # Install HiberProxy Enhanced dependencies
    print_colored("Installing HiberProxy Enhanced dependencies...", Colors.BLUE)
    requirements_file = Path("requirements_enhanced.txt")
    if requirements_file.exists():
        if not run_command(f'"{venv_pip}" install -r requirements_enhanced.txt'):
            return False
    else:
        print_colored("Warning: requirements_enhanced.txt not found, installing basic dependencies", Colors.YELLOW)
        basic_deps = "beautifulsoup4 requests urllib3 pyyaml"
        if not run_command(f'"{venv_pip}" install {basic_deps}'):
            return False
    
    # Install web interface dependencies
    print_colored("Installing web interface dependencies...", Colors.BLUE)
    web_deps = "flask flask-socketio flask-cors"
    if not run_command(f'"{venv_pip}" install {web_deps}'):
        return False
    
    # Install development dependencies
    print_colored("Installing development dependencies...", Colors.BLUE)
    dev_deps = "pytest pytest-cov black flake8"
    if not run_command(f'"{venv_pip}" install {dev_deps}'):
        print_colored("Warning: Failed to install development dependencies", Colors.YELLOW)
    
    return True

def create_activation_script():
    """Create activation script for the virtual environment"""
    print_colored("Creating activation script...", Colors.BLUE)
    
    if platform.system() == "Windows":
        # Windows batch script
        script_content = f"""@echo off
REM HiberProxy Enhanced Environment Activation Script

if exist "{VENV_NAME}" (
    echo Activating HiberProxy Enhanced virtual environment...
    call "{VENV_NAME}\\Scripts\\activate.bat"
    echo Virtual environment activated!
    echo Python path: %VIRTUAL_ENV%\\Scripts\\python.exe
    echo Pip path: %VIRTUAL_ENV%\\Scripts\\pip.exe
    echo.
    echo To start the web interface:
    echo   python hiber_proxy\\web\\run_server.py
    echo.
    echo To deactivate the environment:
    echo   deactivate
) else (
    echo Error: Virtual environment not found!
    echo Please run setup_web_env.py first
    pause
)
"""
        script_name = "activate_hiber_env.bat"
    else:
        # Unix shell script
        script_content = f"""#!/bin/bash
# HiberProxy Enhanced Environment Activation Script

VENV_NAME="{VENV_NAME}"

if [ -d "$VENV_NAME" ]; then
    echo "Activating HiberProxy Enhanced virtual environment..."
    source "$VENV_NAME/bin/activate"
    echo "Virtual environment activated!"
    echo "Python path: $(which python)"
    echo "Pip path: $(which pip)"
    echo ""
    echo "To start the web interface:"
    echo "  python hiber_proxy/web/run_server.py"
    echo ""
    echo "To deactivate the environment:"
    echo "  deactivate"
else
    echo "Error: Virtual environment not found!"
    echo "Please run setup_web_env.py first"
    exit 1
fi
"""
        script_name = "activate_hiber_env.sh"
    
    with open(script_name, 'w') as f:
        f.write(script_content)
    
    # Make executable on Unix systems
    if platform.system() != "Windows":
        os.chmod(script_name, 0o755)
    
    return script_name

def create_requirements_file():
    """Create requirements file for the virtual environment"""
    print_colored("Creating virtual environment requirements file...", Colors.BLUE)
    venv_pip = get_venv_pip()
    
    try:
        requirements = run_command(f'"{venv_pip}" freeze', capture_output=True)
        with open("requirements_web.txt", 'w') as f:
            f.write(requirements)
        return True
    except Exception as e:
        print_colored(f"Warning: Could not create requirements file: {e}", Colors.YELLOW)
        return False

def test_installation():
    """Test that all dependencies are properly installed"""
    print_colored("Testing installation...", Colors.BLUE)
    venv_python = get_venv_python()
    
    test_script = '''
try:
    import flask
    import flask_socketio
    import flask_cors
    from hiber_proxy.main import HiberProxyApp
    print("All imports successful!")
except ImportError as e:
    print(f"Import error: {e}")
    exit(1)
'''
    
    return run_command(f'"{venv_python}" -c "{test_script}"')

def main():
    """Main setup function"""
    print_colored(f"=== {PROJECT_NAME} Web Interface Setup ===", Colors.BLUE)
    print()
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Create virtual environment
    if not create_virtual_environment():
        return 1
    
    # Install dependencies
    if not install_dependencies():
        print_colored("Failed to install dependencies", Colors.RED)
        return 1
    
    # Create activation script
    script_name = create_activation_script()
    
    # Create requirements file
    create_requirements_file()
    
    # Test installation
    if not test_installation():
        print_colored("Installation test failed", Colors.RED)
        return 1
    
    # Success message
    print()
    print_colored("=== Setup Complete! ===", Colors.GREEN)
    print()
    print_colored(f"✓ Virtual environment created: {VENV_NAME}", Colors.GREEN)
    print_colored("✓ All dependencies installed", Colors.GREEN)
    print_colored(f"✓ Activation script created: {script_name}", Colors.GREEN)
    print()
    print_colored("Next steps:", Colors.BLUE)
    
    if platform.system() == "Windows":
        print("1. Activate the environment:")
        print_colored(f"   {script_name}", Colors.YELLOW)
        print()
        print("2. Start the web interface:")
        print_colored("   python hiber_proxy\\web\\run_server.py", Colors.YELLOW)
    else:
        print("1. Activate the environment:")
        print_colored(f"   source {script_name}", Colors.YELLOW)
        print()
        print("2. Start the web interface:")
        print_colored("   python hiber_proxy/web/run_server.py", Colors.YELLOW)
    
    print()
    print("3. Open your browser to:")
    print_colored("   http://localhost:5000", Colors.YELLOW)
    print()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
