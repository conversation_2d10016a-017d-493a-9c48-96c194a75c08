#!/bin/bash
# HiberProxy Enhanced Web Interface Setup Script
# This script creates a Python virtual environment and installs all required dependencies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project configuration
PROJECT_NAME="HiberProxy Enhanced"
VENV_NAME="hiber_venv"
PYTHON_CMD="python3"

echo -e "${BLUE}=== $PROJECT_NAME Web Interface Setup ===${NC}"
echo

# Check if Python 3 is available
if ! command -v $PYTHON_CMD &> /dev/null; then
    echo -e "${RED}Error: Python 3 is not installed or not in PATH${NC}"
    echo "Please install Python 3 first:"
    echo "  sudo apt update && sudo apt install python3 python3-pip python3-venv"
    exit 1
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
echo -e "${BLUE}Found Python version: $PYTHON_VERSION${NC}"

# Check if python3-venv is available
if ! $PYTHON_CMD -m venv --help &> /dev/null; then
    echo -e "${YELLOW}Warning: python3-venv module not found${NC}"
    echo "Installing python3-venv..."
    sudo apt update && sudo apt install python3-venv
fi

# Create virtual environment
echo -e "${BLUE}Creating virtual environment: $VENV_NAME${NC}"
if [ -d "$VENV_NAME" ]; then
    echo -e "${YELLOW}Virtual environment already exists. Removing old one...${NC}"
    rm -rf "$VENV_NAME"
fi

$PYTHON_CMD -m venv "$VENV_NAME"
echo -e "${GREEN}✓ Virtual environment created${NC}"

# Activate virtual environment
echo -e "${BLUE}Activating virtual environment...${NC}"
source "$VENV_NAME/bin/activate"

# Upgrade pip
echo -e "${BLUE}Upgrading pip...${NC}"
pip install --upgrade pip

# Install core dependencies first
echo -e "${BLUE}Installing core dependencies...${NC}"
pip install wheel setuptools

# Install HiberProxy Enhanced dependencies
echo -e "${BLUE}Installing HiberProxy Enhanced dependencies...${NC}"
if [ -f "requirements_enhanced.txt" ]; then
    pip install -r requirements_enhanced.txt
else
    echo -e "${YELLOW}Warning: requirements_enhanced.txt not found, installing basic dependencies${NC}"
    pip install beautifulsoup4 requests urllib3 pyyaml
fi

# Install web interface specific dependencies
echo -e "${BLUE}Installing web interface dependencies...${NC}"
pip install flask flask-socketio flask-cors

# Install optional development dependencies
echo -e "${BLUE}Installing development dependencies...${NC}"
pip install pytest pytest-cov black flake8

# Create activation script
echo -e "${BLUE}Creating activation script...${NC}"
cat > activate_hiber_env.sh << 'EOF'
#!/bin/bash
# HiberProxy Enhanced Environment Activation Script

VENV_NAME="hiber_venv"

if [ -d "$VENV_NAME" ]; then
    echo "Activating HiberProxy Enhanced virtual environment..."
    source "$VENV_NAME/bin/activate"
    echo "Virtual environment activated!"
    echo "Python path: $(which python)"
    echo "Pip path: $(which pip)"
    echo ""
    echo "To start the web interface:"
    echo "  python hiber_proxy/web/run_server.py"
    echo ""
    echo "To deactivate the environment:"
    echo "  deactivate"
else
    echo "Error: Virtual environment not found!"
    echo "Please run setup_web_env.sh first"
    exit 1
fi
EOF

chmod +x activate_hiber_env.sh

# Create requirements file for the virtual environment
echo -e "${BLUE}Creating virtual environment requirements file...${NC}"
pip freeze > requirements_web.txt

# Test the installation
echo -e "${BLUE}Testing installation...${NC}"
python -c "
try:
    import flask
    import flask_socketio
    import flask_cors
    from hiber_proxy.main import HiberProxyApp
    print('✓ All imports successful!')
except ImportError as e:
    print(f'✗ Import error: {e}')
    exit(1)
"

echo
echo -e "${GREEN}=== Setup Complete! ===${NC}"
echo
echo -e "${GREEN}✓ Virtual environment created: $VENV_NAME${NC}"
echo -e "${GREEN}✓ All dependencies installed${NC}"
echo -e "${GREEN}✓ Activation script created: activate_hiber_env.sh${NC}"
echo
echo -e "${BLUE}Next steps:${NC}"
echo "1. Activate the environment:"
echo -e "   ${YELLOW}source activate_hiber_env.sh${NC}"
echo
echo "2. Start the web interface:"
echo -e "   ${YELLOW}python hiber_proxy/web/run_server.py${NC}"
echo
echo "3. Open your browser to:"
echo -e "   ${YELLOW}http://localhost:5000${NC}"
echo
echo -e "${BLUE}To activate the environment in the future:${NC}"
echo -e "   ${YELLOW}source activate_hiber_env.sh${NC}"
echo
echo -e "${BLUE}To deactivate the environment:${NC}"
echo -e "   ${YELLOW}deactivate${NC}"
echo
