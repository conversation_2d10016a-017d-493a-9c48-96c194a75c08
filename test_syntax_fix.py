#!/usr/bin/env python3
"""
Test script to verify the syntax fix for the virtual environment setup
"""

import subprocess
import sys
import os
from pathlib import Path

def test_python_syntax():
    """Test that the Python setup script has valid syntax"""
    print("Testing Python setup script syntax...")
    
    try:
        # Test syntax by compiling the file
        with open('setup_web_env.py', 'r') as f:
            code = f.read()
        
        compile(code, 'setup_web_env.py', 'exec')
        print("✅ Python setup script syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"❌ Syntax error in setup_web_env.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Error checking syntax: {e}")
        return False

def test_virtual_env_imports():
    """Test imports in the virtual environment"""
    print("\nTesting virtual environment imports...")
    
    venv_python = "hiber_venv/bin/python"
    if not Path(venv_python).exists():
        print("⚠️  Virtual environment not found, skipping import test")
        return True
    
    # Test individual imports
    imports_to_test = [
        ("Flask", "import flask; print('Flask imported successfully')"),
        ("Flask-SocketIO", "import flask_socketio; print('Flask-SocketIO imported successfully')"),
        ("Flask-CORS", "import flask_cors; print('Flask-CORS imported successfully')"),
        ("HiberProxy", "from hiber_proxy.main import HiberProxyApp; print('HiberProxy imported successfully')"),
    ]
    
    all_passed = True
    for name, test_code in imports_to_test:
        try:
            result = subprocess.run(
                [venv_python, "-c", test_code],
                capture_output=True,
                text=True,
                check=True,
                timeout=30
            )
            print(f"✅ {name}: {result.stdout.strip()}")
        except subprocess.CalledProcessError as e:
            print(f"❌ {name}: {e.stderr.strip()}")
            all_passed = False
        except subprocess.TimeoutExpired:
            print(f"❌ {name}: Import test timed out")
            all_passed = False
        except Exception as e:
            print(f"❌ {name}: {str(e)}")
            all_passed = False
    
    return all_passed

def test_web_interface_creation():
    """Test that the web interface can be created"""
    print("\nTesting web interface creation...")
    
    venv_python = "hiber_venv/bin/python"
    if not Path(venv_python).exists():
        print("⚠️  Virtual environment not found, skipping web interface test")
        return True
    
    test_code = """
try:
    from hiber_proxy.web.app import create_app
    app, socketio = create_app()
    print('Web interface created successfully')
    print(f'Flask app name: {app.name}')
    print(f'SocketIO initialized: {socketio is not None}')
except Exception as e:
    print(f'Error: {e}')
    exit(1)
"""
    
    try:
        result = subprocess.run(
            [venv_python, "-c", test_code],
            capture_output=True,
            text=True,
            check=True,
            timeout=30
        )
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                print(f"✅ {line}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Web interface creation failed: {e.stderr.strip()}")
        return False
    except subprocess.TimeoutExpired:
        print("❌ Web interface test timed out")
        return False
    except Exception as e:
        print(f"❌ Web interface test error: {str(e)}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Syntax Fix for Virtual Environment Setup")
    print("=" * 60)
    
    tests = [
        ("Python Syntax", test_python_syntax),
        ("Virtual Environment Imports", test_virtual_env_imports),
        ("Web Interface Creation", test_web_interface_creation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            print()
    
    print("=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The syntax fix is working correctly.")
        print("\n📝 Next steps:")
        print("1. Activate the virtual environment: source activate_hiber_env.sh")
        print("2. Start the web interface: python hiber_proxy/web/run_server.py")
        print("3. Open browser to: http://localhost:5000")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
