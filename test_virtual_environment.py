#!/usr/bin/env python3
"""
Test script for HiberProxy Enhanced Virtual Environment Setup

This script verifies that the virtual environment is properly configured
and all dependencies are working correctly.
"""

import sys
import os
import subprocess
from pathlib import Path

def print_header(title):
    """Print a formatted header"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_success(message):
    """Print success message"""
    print(f"✅ {message}")

def print_error(message):
    """Print error message"""
    print(f"❌ {message}")

def print_warning(message):
    """Print warning message"""
    print(f"⚠️  {message}")

def print_info(message):
    """Print info message"""
    print(f"ℹ️  {message}")

def check_virtual_environment():
    """Check if virtual environment exists and is properly configured"""
    print_header("Virtual Environment Check")
    
    venv_path = Path("hiber_venv")
    if not venv_path.exists():
        print_error("Virtual environment not found!")
        print_info("Run: python3 setup_web_env.py")
        return False
    
    print_success(f"Virtual environment found: {venv_path}")
    
    # Check Python executable
    if os.name == 'nt':  # Windows
        python_exe = venv_path / "Scripts" / "python.exe"
        pip_exe = venv_path / "Scripts" / "pip.exe"
    else:  # Unix/Linux/macOS
        python_exe = venv_path / "bin" / "python"
        pip_exe = venv_path / "bin" / "pip"
    
    if python_exe.exists():
        print_success(f"Python executable found: {python_exe}")
    else:
        print_error(f"Python executable not found: {python_exe}")
        return False
    
    if pip_exe.exists():
        print_success(f"Pip executable found: {pip_exe}")
    else:
        print_error(f"Pip executable not found: {pip_exe}")
        return False
    
    return True

def test_dependencies():
    """Test that all required dependencies are installed"""
    print_header("Dependency Check")
    
    venv_python = "hiber_venv/bin/python" if os.name != 'nt' else "hiber_venv\\Scripts\\python.exe"
    
    dependencies = [
        ("Flask", "import flask; print(f'Flask {flask.__version__}')"),
        ("Flask-SocketIO", "import flask_socketio; print('Flask-SocketIO imported')"),
        ("Flask-CORS", "import flask_cors; print('Flask-CORS imported')"),
        ("BeautifulSoup4", "import bs4; print(f'BeautifulSoup4 {bs4.__version__}')"),
        ("Requests", "import requests; print(f'Requests {requests.__version__}')"),
        ("PyYAML", "import yaml; print('PyYAML imported')"),
        ("psutil", "import psutil; print(f'psutil {psutil.__version__}')"),
    ]
    
    all_passed = True
    for name, test_code in dependencies:
        try:
            result = subprocess.run(
                [venv_python, "-c", test_code],
                capture_output=True,
                text=True,
                check=True
            )
            print_success(f"{name}: {result.stdout.strip()}")
        except subprocess.CalledProcessError as e:
            print_error(f"{name}: {e.stderr.strip()}")
            all_passed = False
        except Exception as e:
            print_error(f"{name}: {str(e)}")
            all_passed = False
    
    return all_passed

def test_hiber_proxy_import():
    """Test HiberProxy Enhanced import"""
    print_header("HiberProxy Enhanced Import Test")
    
    venv_python = "hiber_venv/bin/python" if os.name != 'nt' else "hiber_venv\\Scripts\\python.exe"
    
    test_code = """
try:
    from hiber_proxy.main import HiberProxyApp
    app = HiberProxyApp()
    stats = app.get_statistics()
    print(f"HiberProxy Enhanced initialized successfully!")
    print(f"Total proxies: {stats.get('total_proxies', 0)}")
    print(f"Working proxies: {stats.get('working_proxies', 0)}")
except Exception as e:
    print(f"Error: {e}")
    exit(1)
"""
    
    try:
        result = subprocess.run(
            [venv_python, "-c", test_code],
            capture_output=True,
            text=True,
            check=True
        )
        for line in result.stdout.strip().split('\n'):
            print_success(line)
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"HiberProxy import failed: {e.stderr.strip()}")
        return False

def test_web_interface_import():
    """Test web interface import"""
    print_header("Web Interface Import Test")
    
    venv_python = "hiber_venv/bin/python" if os.name != 'nt' else "hiber_venv\\Scripts\\python.exe"
    
    test_code = """
try:
    from hiber_proxy.web.app import create_app
    app, socketio = create_app()
    print("Web interface imports successful!")
    print(f"Flask app: {app.name}")
    print(f"SocketIO initialized: {socketio is not None}")
except Exception as e:
    print(f"Error: {e}")
    exit(1)
"""
    
    try:
        result = subprocess.run(
            [venv_python, "-c", test_code],
            capture_output=True,
            text=True,
            check=True
        )
        for line in result.stdout.strip().split('\n'):
            print_success(line)
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"Web interface import failed: {e.stderr.strip()}")
        return False

def check_activation_scripts():
    """Check if activation scripts exist"""
    print_header("Activation Scripts Check")
    
    scripts = []
    if os.name == 'nt':  # Windows
        scripts.append("activate_hiber_env.bat")
    else:  # Unix/Linux/macOS
        scripts.append("activate_hiber_env.sh")
    
    all_found = True
    for script in scripts:
        script_path = Path(script)
        if script_path.exists():
            print_success(f"Activation script found: {script}")
        else:
            print_error(f"Activation script not found: {script}")
            all_found = False
    
    return all_found

def print_usage_instructions():
    """Print usage instructions"""
    print_header("Usage Instructions")
    
    print("🚀 To start the web interface:")
    print()
    
    if os.name == 'nt':  # Windows
        print("1. Activate the virtual environment:")
        print("   activate_hiber_env.bat")
        print()
        print("2. Start the web server:")
        print("   python hiber_proxy\\web\\run_server.py")
    else:  # Unix/Linux/macOS
        print("1. Activate the virtual environment:")
        print("   source activate_hiber_env.sh")
        print()
        print("2. Start the web server:")
        print("   python hiber_proxy/web/run_server.py")
    
    print()
    print("3. Open your browser to:")
    print("   http://localhost:5000")
    print()
    print("📝 Alternative direct command:")
    if os.name == 'nt':
        print("   hiber_venv\\Scripts\\python.exe hiber_proxy\\web\\run_server.py")
    else:
        print("   hiber_venv/bin/python hiber_proxy/web/run_server.py")

def main():
    """Main test function"""
    print_header("HiberProxy Enhanced Virtual Environment Test")
    print("Testing virtual environment setup and dependencies...")
    
    tests = [
        ("Virtual Environment", check_virtual_environment),
        ("Dependencies", test_dependencies),
        ("HiberProxy Import", test_hiber_proxy_import),
        ("Web Interface Import", test_web_interface_import),
        ("Activation Scripts", check_activation_scripts),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print_error(f"{test_name} test failed")
        except Exception as e:
            print_error(f"{test_name} test error: {e}")
        print()
    
    # Results
    print_header("Test Results")
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print_success("All tests passed! Virtual environment is ready.")
        print_usage_instructions()
        return 0
    else:
        print_error("Some tests failed. Please check the errors above.")
        print()
        print_info("To recreate the virtual environment:")
        print("   python3 setup_web_env.py")
        return 1

if __name__ == "__main__":
    sys.exit(main())
