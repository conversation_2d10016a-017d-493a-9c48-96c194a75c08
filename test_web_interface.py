#!/usr/bin/env python3
"""
Test script for HiberProxy Enhanced Web Interface

This script tests the web interface functionality without requiring
the full Flask dependencies to be installed.
"""

import sys
import os
from pathlib import Path

# Add hiber_proxy to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all web interface modules can be imported"""
    print("Testing web interface imports...")
    
    try:
        # Test core imports
        from hiber_proxy.main import HiberProxyApp
        print("✓ HiberProxyApp import successful")
        
        # Test web module structure
        from hiber_proxy.web import create_app
        print("✓ Web app creation import successful")
        
        # Test API routes
        from hiber_proxy.web.api.routes import api_bp
        print("✓ API routes import successful")
        
        print("✓ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def test_file_structure():
    """Test that all required files exist"""
    print("\nTesting file structure...")
    
    required_files = [
        'hiber_proxy/web/__init__.py',
        'hiber_proxy/web/app.py',
        'hiber_proxy/web/run_server.py',
        'hiber_proxy/web/api/__init__.py',
        'hiber_proxy/web/api/routes.py',
        'hiber_proxy/web/templates/base.html',
        'hiber_proxy/web/templates/index.html',
        'hiber_proxy/web/templates/proxies.html',
        'hiber_proxy/web/templates/config.html',
        'hiber_proxy/web/templates/analytics.html',
        'hiber_proxy/web/static/css/style.css',
        'hiber_proxy/web/static/js/main.js',
        'hiber_proxy/web/static/js/websocket.js'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✓ {file_path}")
    
    if missing_files:
        print(f"\n✗ Missing files:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✓ All required files exist!")
        return True

def test_app_creation():
    """Test that the Flask app can be created"""
    print("\nTesting Flask app creation...")
    
    try:
        # This will fail if Flask is not installed, but we can catch that
        from hiber_proxy.web.app import create_app
        
        # Try to create the app (this might fail due to missing dependencies)
        try:
            app, socketio = create_app()
            print("✓ Flask app created successfully!")
            print(f"✓ App name: {app.name}")
            print(f"✓ SocketIO initialized: {socketio is not None}")
            return True
        except ImportError as e:
            print(f"⚠ Flask dependencies not installed: {e}")
            print("  Install with: pip install flask flask-socketio flask-cors")
            return False
        except Exception as e:
            print(f"✗ Error creating app: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ Cannot import create_app: {e}")
        return False

def test_hiber_app():
    """Test that HiberProxy app can be initialized"""
    print("\nTesting HiberProxy app initialization...")
    
    try:
        from hiber_proxy.main import HiberProxyApp
        
        # Try to create HiberProxy app
        app = HiberProxyApp()
        print("✓ HiberProxyApp initialized successfully!")
        
        # Test basic functionality
        stats = app.get_statistics()
        print(f"✓ Statistics retrieved: {len(stats)} metrics")
        
        return True
        
    except Exception as e:
        print(f"✗ Error initializing HiberProxy app: {e}")
        return False

def main():
    """Run all tests"""
    print("HiberProxy Enhanced Web Interface Test")
    print("=" * 50)
    
    tests = [
        test_file_structure,
        test_imports,
        test_hiber_app,
        test_app_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! Web interface is ready.")
        print("\nTo start the web interface:")
        print("1. Install dependencies: pip install -r requirements_enhanced.txt")
        print("2. Run: python hiber_proxy/web/run_server.py")
        print("3. Open: http://localhost:5000")
    else:
        print("✗ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == '__main__':
    sys.exit(main())
