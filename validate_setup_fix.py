#!/usr/bin/env python3
"""
Validation script for the syntax fix in HiberProxy Enhanced setup

This script validates that the syntax error has been fixed and the
virtual environment setup works correctly.
"""

import subprocess
import sys
import os
import tempfile
from pathlib import Path

def print_header(title):
    """Print formatted header"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print('='*60)

def print_success(msg):
    """Print success message"""
    print(f"✅ {msg}")

def print_error(msg):
    """Print error message"""
    print(f"❌ {msg}")

def print_info(msg):
    """Print info message"""
    print(f"ℹ️  {msg}")

def test_syntax_error_fix():
    """Test that the syntax error in the setup script is fixed"""
    print_header("Testing Syntax Error Fix")
    
    # Test the problematic code pattern that was causing the error
    test_code = '''
try:
    import flask
    import flask_socketio
    import flask_cors
    from hiber_proxy.main import HiberProxyApp
    print("All imports successful!")
except ImportError as e:
    print("Import error: " + str(e))
    exit(1)
except Exception as e:
    print("Unexpected error: " + str(e))
    exit(1)
'''
    
    try:
        # Test syntax by compiling
        compile(test_code, '<test>', 'exec')
        print_success("Test code syntax is valid")
        
        # Test execution if virtual environment exists
        venv_python = Path("hiber_venv/bin/python")
        if venv_python.exists():
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(test_code)
                temp_file = f.name
            
            try:
                result = subprocess.run(
                    [str(venv_python), temp_file],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                if result.returncode == 0:
                    print_success(f"Test execution successful: {result.stdout.strip()}")
                else:
                    print_error(f"Test execution failed: {result.stderr.strip()}")
                    return False
                    
            finally:
                try:
                    os.unlink(temp_file)
                except OSError:
                    pass
        else:
            print_info("Virtual environment not found, skipping execution test")
        
        return True
        
    except SyntaxError as e:
        print_error(f"Syntax error still present: {e}")
        return False
    except Exception as e:
        print_error(f"Unexpected error: {e}")
        return False

def test_setup_script_syntax():
    """Test that the setup script itself has valid syntax"""
    print_header("Testing Setup Script Syntax")
    
    scripts_to_test = [
        "setup_web_env.py",
        "test_virtual_environment.py",
        "test_syntax_fix.py"
    ]
    
    all_valid = True
    for script in scripts_to_test:
        if Path(script).exists():
            try:
                with open(script, 'r') as f:
                    code = f.read()
                compile(code, script, 'exec')
                print_success(f"{script} syntax is valid")
            except SyntaxError as e:
                print_error(f"{script} has syntax error: {e}")
                all_valid = False
            except Exception as e:
                print_error(f"Error checking {script}: {e}")
                all_valid = False
        else:
            print_info(f"{script} not found, skipping")
    
    return all_valid

def test_virtual_environment_functionality():
    """Test that the virtual environment works correctly"""
    print_header("Testing Virtual Environment Functionality")
    
    venv_path = Path("hiber_venv")
    if not venv_path.exists():
        print_info("Virtual environment not found, skipping functionality test")
        return True
    
    venv_python = venv_path / "bin" / "python"
    if not venv_python.exists():
        venv_python = venv_path / "Scripts" / "python.exe"  # Windows
    
    if not venv_python.exists():
        print_error("Python executable not found in virtual environment")
        return False
    
    # Test basic Python functionality
    try:
        result = subprocess.run(
            [str(venv_python), "-c", "print('Python works')"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print_success(f"Python execution: {result.stdout.strip()}")
        else:
            print_error(f"Python execution failed: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print_error(f"Error testing Python: {e}")
        return False
    
    # Test Flask import
    try:
        result = subprocess.run(
            [str(venv_python), "-c", "import flask; print(f'Flask {flask.__version__}')"],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        if result.returncode == 0:
            print_success(f"Flask import: {result.stdout.strip()}")
        else:
            print_error(f"Flask import failed: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print_error(f"Error testing Flask: {e}")
        return False
    
    return True

def test_web_interface_startup():
    """Test that the web interface can start without syntax errors"""
    print_header("Testing Web Interface Startup")
    
    venv_path = Path("hiber_venv")
    if not venv_path.exists():
        print_info("Virtual environment not found, skipping web interface test")
        return True
    
    venv_python = venv_path / "bin" / "python"
    if not venv_python.exists():
        venv_python = venv_path / "Scripts" / "python.exe"  # Windows
    
    # Test web interface import without starting server
    test_code = '''
try:
    from hiber_proxy.web.app import create_app
    app, socketio = create_app()
    print("Web interface created successfully")
    print(f"App name: {app.name}")
    print(f"SocketIO: {socketio is not None}")
except Exception as e:
    print(f"Error: {e}")
    exit(1)
'''
    
    try:
        result = subprocess.run(
            [str(venv_python), "-c", test_code],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print_success(line)
            return True
        else:
            print_error(f"Web interface startup failed: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print_error(f"Error testing web interface: {e}")
        return False

def main():
    """Main validation function"""
    print_header("HiberProxy Enhanced Setup Syntax Fix Validation")
    print("This script validates that the syntax error has been fixed")
    print("and the virtual environment setup works correctly.")
    
    tests = [
        ("Syntax Error Fix", test_syntax_error_fix),
        ("Setup Script Syntax", test_setup_script_syntax),
        ("Virtual Environment Functionality", test_virtual_environment_functionality),
        ("Web Interface Startup", test_web_interface_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print_error(f"{test_name} test failed")
        except Exception as e:
            print_error(f"{test_name} test error: {e}")
        print()
    
    print_header("Validation Results")
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print_success("All validation tests passed!")
        print()
        print("🎉 The syntax error has been successfully fixed!")
        print()
        print("📝 Summary of fixes:")
        print("   • Fixed malformed print statement in test script")
        print("   • Replaced f-strings with string concatenation in test code")
        print("   • Added proper exception handling")
        print("   • Used temporary files to avoid quote escaping issues")
        print()
        print("🚀 Next steps:")
        print("   1. Activate virtual environment: source activate_hiber_env.sh")
        print("   2. Start web interface: python hiber_proxy/web/run_server.py")
        print("   3. Open browser to: http://localhost:5000")
        return 0
    else:
        print_error("Some validation tests failed")
        print()
        print("🔧 Troubleshooting:")
        print("   • Check that virtual environment exists: ls hiber_venv/")
        print("   • Recreate environment: python3 setup_web_env.py")
        print("   • Check Python version: python3 --version")
        return 1

if __name__ == "__main__":
    sys.exit(main())
